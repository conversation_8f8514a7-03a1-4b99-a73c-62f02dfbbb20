#!/bin/bash

# 连接问题诊断脚本
# 用于诊断和修复VPS监控连接问题

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查配置
check_configuration() {
    print_message "$BLUE" "检查配置信息..."
    
    local config_file="$HOME/.cf-vps-monitor/config"
    if [[ ! -f "$config_file" ]]; then
        print_message "$RED" "✗ 配置文件不存在: $config_file"
        return 1
    fi
    
    source "$config_file"
    
    print_message "$CYAN" "当前配置:"
    echo "  Worker URL: ${WORKER_URL:-未设置}"
    echo "  Server ID: ${SERVER_ID:-未设置}"
    echo "  API Key: ${API_KEY:0:8}...（已隐藏）"
    echo "  上报间隔: ${INTERVAL:-未设置}秒"
    
    # 检查必需配置
    local missing_config=()
    [[ -z "${WORKER_URL:-}" ]] && missing_config+=("WORKER_URL")
    [[ -z "${SERVER_ID:-}" ]] && missing_config+=("SERVER_ID")
    [[ -z "${API_KEY:-}" ]] && missing_config+=("API_KEY")
    
    if [[ ${#missing_config[@]} -gt 0 ]]; then
        print_message "$RED" "✗ 缺少必需配置: ${missing_config[*]}"
        return 1
    fi
    
    print_message "$GREEN" "✓ 配置检查通过"
    return 0
}

# 测试网络连接
test_network_connectivity() {
    print_message "$BLUE" "测试网络连接..."
    
    if [[ -z "${WORKER_URL:-}" ]]; then
        print_message "$RED" "✗ Worker URL未设置"
        return 1
    fi
    
    # 提取域名
    local domain=$(echo "$WORKER_URL" | sed 's|https\?://||' | sed 's|/.*||')
    
    # 测试DNS解析
    if command -v nslookup >/dev/null 2>&1; then
        if nslookup "$domain" >/dev/null 2>&1; then
            print_message "$GREEN" "✓ DNS解析成功: $domain"
        else
            print_message "$RED" "✗ DNS解析失败: $domain"
            return 1
        fi
    fi
    
    # 测试HTTP连接
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$WORKER_URL" 2>/dev/null || echo "000")
    case "$response" in
        200|301|302|404|405)
            print_message "$GREEN" "✓ HTTP连接成功 (状态码: $response)"
            ;;
        000)
            print_message "$RED" "✗ HTTP连接失败"
            return 1
            ;;
        *)
            print_message "$YELLOW" "⚠ HTTP连接异常 (状态码: $response)"
            ;;
    esac
    
    return 0
}

# 测试API端点
test_api_endpoints() {
    print_message "$BLUE" "测试API端点..."
    
    # 测试配置获取API
    print_message "$CYAN" "测试配置获取API..."
    local config_response=$(curl -s -w "%{http_code}" \
        -H "X-API-Key: $API_KEY" \
        "$WORKER_URL/api/config/$SERVER_ID" 2>/dev/null || echo "000")
    
    local config_http_code="${config_response: -3}"
    local config_body="${config_response%???}"
    
    case "$config_http_code" in
        200)
            print_message "$GREEN" "✓ 配置获取API正常"
            echo "  响应: $config_body"
            ;;
        401)
            print_message "$RED" "✗ API密钥无效"
            print_message "$YELLOW" "建议: 检查API密钥是否正确"
            ;;
        404)
            print_message "$RED" "✗ 服务器ID不存在"
            print_message "$YELLOW" "建议: 检查服务器ID是否正确，或在面板中创建新服务器"
            ;;
        *)
            print_message "$RED" "✗ 配置获取API失败 (HTTP $config_http_code)"
            echo "  响应: $config_body"
            ;;
    esac
    
    # 测试数据上报API
    print_message "$CYAN" "测试数据上报API..."
    local test_data='{"timestamp":1640995200,"cpu":{"usage_percent":10.5,"load_avg":[0.1,0.2,0.3]},"memory":{"total":1000,"used":500,"free":500,"usage_percent":50.0},"disk":{"total":10.0,"used":5.0,"free":5.0,"usage_percent":50},"network":{"upload_speed":1000,"download_speed":2000,"total_upload":1000000,"total_download":2000000},"uptime":3600}'
    
    local report_response=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d "$test_data" \
        "$WORKER_URL/api/report/$SERVER_ID" 2>/dev/null || echo "000")
    
    local report_http_code="${report_response: -3}"
    local report_body="${report_response%???}"
    
    case "$report_http_code" in
        200)
            print_message "$GREEN" "✓ 数据上报API正常"
            echo "  响应: $report_body"
            ;;
        401)
            print_message "$RED" "✗ API密钥无效"
            ;;
        404)
            print_message "$RED" "✗ 服务器ID不存在"
            ;;
        *)
            print_message "$RED" "✗ 数据上报API失败 (HTTP $report_http_code)"
            echo "  响应: $report_body"
            ;;
    esac
}

# 提供修复建议
provide_fix_suggestions() {
    print_message "$BLUE" "修复建议..."
    
    print_message "$CYAN" "如果遇到404错误（服务器不存在）："
    echo "1. 登录监控面板，检查服务器列表"
    echo "2. 确认服务器ID是否正确"
    echo "3. 如果服务器不存在，请在面板中添加新服务器"
    echo "4. 重新运行配置命令: ./cf-vps-monitor.sh config"
    
    print_message "$CYAN" "如果遇到401错误（认证失败）："
    echo "1. 检查API密钥是否正确"
    echo "2. 确认API密钥没有过期"
    echo "3. 重新获取API密钥并更新配置"
    
    print_message "$CYAN" "如果遇到网络连接问题："
    echo "1. 检查防火墙设置"
    echo "2. 确认可以访问外网"
    echo "3. 检查代理设置"
    echo "4. 尝试使用不同的DNS服务器"
    
    print_message "$CYAN" "通用解决方案："
    echo "1. 重新配置: ./cf-vps-monitor.sh config"
    echo "2. 测试连接: ./cf-vps-monitor.sh test"
    echo "3. 查看日志: ./cf-vps-monitor.sh logs"
    echo "4. 重启服务: ./cf-vps-monitor.sh restart"
}

# 自动修复尝试
attempt_auto_fix() {
    print_message "$BLUE" "尝试自动修复..."
    
    # 检查是否有备用配置
    local backup_config="$HOME/.cf-vps-monitor/config.backup"
    if [[ -f "$backup_config" ]]; then
        print_message "$CYAN" "发现备用配置文件"
        read -p "是否尝试使用备用配置？(y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            cp "$backup_config" "$HOME/.cf-vps-monitor/config"
            print_message "$GREEN" "✓ 已恢复备用配置"
            return 0
        fi
    fi
    
    # 提示重新配置
    print_message "$CYAN" "建议重新进行配置"
    read -p "是否现在重新配置？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [[ -f "./cf-vps-monitor.sh" ]]; then
            ./cf-vps-monitor.sh config
        else
            print_message "$RED" "找不到主脚本文件"
        fi
    fi
}

# 主函数
main() {
    print_message "$BLUE" "=================================="
    print_message "$BLUE" "    VPS监控连接问题诊断"
    print_message "$BLUE" "=================================="
    echo
    
    local issues_found=0
    
    # 检查配置
    if ! check_configuration; then
        ((issues_found++))
    fi
    echo
    
    # 如果配置正常，继续测试
    if [[ $issues_found -eq 0 ]]; then
        # 测试网络连接
        if ! test_network_connectivity; then
            ((issues_found++))
        fi
        echo
        
        # 测试API端点
        test_api_endpoints
        echo
    fi
    
    # 提供修复建议
    provide_fix_suggestions
    echo
    
    # 尝试自动修复
    if [[ $issues_found -gt 0 ]]; then
        attempt_auto_fix
    fi
    
    print_message "$BLUE" "=================================="
    if [[ $issues_found -eq 0 ]]; then
        print_message "$GREEN" "✅ 诊断完成，未发现严重问题"
    else
        print_message "$YELLOW" "⚠ 诊断完成，发现 $issues_found 个问题"
        print_message "$CYAN" "请根据上述建议进行修复"
    fi
    print_message "$BLUE" "=================================="
}

# 显示帮助
show_help() {
    echo "VPS监控连接问题诊断脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo
    echo "此脚本将诊断VPS监控系统的连接问题，包括："
    echo "  - 配置文件检查"
    echo "  - 网络连接测试"
    echo "  - API端点测试"
    echo "  - 自动修复建议"
}

# 检查参数
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# 运行主函数
main
