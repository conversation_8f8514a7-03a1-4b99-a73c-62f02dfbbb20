#!/bin/bash

# 数据收集功能测试脚本
# 用于验证优化后的数据收集算法

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 从主脚本加载函数
load_monitoring_functions() {
    if [[ ! -f "cf-vps-monitor.sh" ]]; then
        print_message "$RED" "错误: 找不到 cf-vps-monitor.sh 文件"
        exit 1
    fi
    
    # 提取需要的函数
    source <(awk '
    /^# 检测系统信息/,/^}/ { print; next }
    /^# 获取CPU使用率/,/^}/ { print; next }
    /^# 获取内存使用情况/,/^}/ { print; next }
    /^# 获取磁盘使用情况/,/^}/ { print; next }
    /^# 获取网络使用情况/,/^}/ { print; next }
    /^# 获取系统运行时间/,/^}/ { print; next }
    /^# 验证和清理数值/,/^}/ { print; next }
    /^# 验证和清理整数/,/^}/ { print; next }
    /^# 清理JSON字符串/,/^}/ { print; next }
    /^# 验证JSON数据格式/,/^}/ { print; next }
    /^# 生成默认JSON数据/,/^}/ { print; next }
    /^command_exists\(\)/ { print; getline; print; getline; print; next }
    ' cf-vps-monitor.sh)
    
    # 设置系统检测
    OS=$(uname -s)
    export OS
}

# 测试单个数据收集函数
test_function() {
    local func_name="$1"
    local description="$2"
    
    print_message "$BLUE" "测试 $description..."
    
    local start_time=$(date +%s.%N)
    local result
    local status=0
    
    if result=$($func_name 2>&1); then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "0")
        
        print_message "$GREEN" "✓ $description 成功 (${duration}s)"
        echo "  结果: $result"
        
        # 验证JSON格式
        if echo "$result" | python3 -m json.tool >/dev/null 2>&1; then
            print_message "$GREEN" "  ✓ JSON格式有效"
        elif command -v jq >/dev/null 2>&1 && echo "$result" | jq . >/dev/null 2>&1; then
            print_message "$GREEN" "  ✓ JSON格式有效"
        else
            print_message "$YELLOW" "  ⚠ JSON格式可能无效"
        fi
    else
        print_message "$RED" "✗ $description 失败"
        echo "  错误: $result"
        status=1
    fi
    
    echo
    return $status
}

# 测试数据验证函数
test_validation() {
    print_message "$BLUE" "测试数据验证功能..."
    
    # 测试有效数据
    local valid_cpu='{"usage_percent":25.5,"load_avg":[0.1,0.2,0.3]}'
    if validate_json_data "$valid_cpu" "cpu"; then
        print_message "$GREEN" "✓ 有效CPU数据验证通过"
    else
        print_message "$RED" "✗ 有效CPU数据验证失败"
    fi
    
    # 测试无效数据
    local invalid_cpu='{"invalid":"data"}'
    if ! validate_json_data "$invalid_cpu" "cpu"; then
        print_message "$GREEN" "✓ 无效CPU数据正确被拒绝"
    else
        print_message "$RED" "✗ 无效CPU数据错误通过验证"
    fi
    
    # 测试默认数据生成
    local default_memory=$(generate_default_json "memory")
    if validate_json_data "$default_memory" "memory"; then
        print_message "$GREEN" "✓ 默认内存数据生成正确"
        echo "  默认数据: $default_memory"
    else
        print_message "$RED" "✗ 默认内存数据生成失败"
    fi
    
    echo
}

# 测试数据一致性
test_data_consistency() {
    print_message "$BLUE" "测试数据一致性..."
    
    # 多次获取内存数据，检查一致性
    local memory1=$(get_memory_usage)
    sleep 1
    local memory2=$(get_memory_usage)
    
    if [[ -n "$memory1" && -n "$memory2" ]]; then
        print_message "$GREEN" "✓ 内存数据获取一致"
        
        # 检查数据合理性
        if command -v jq >/dev/null 2>&1; then
            local total1=$(echo "$memory1" | jq -r '.total')
            local used1=$(echo "$memory1" | jq -r '.used')
            local free1=$(echo "$memory1" | jq -r '.free')
            
            if [[ "$total1" != "null" && "$used1" != "null" && "$free1" != "null" ]]; then
                local sum=$((used1 + free1))
                if [[ $sum -eq $total1 ]]; then
                    print_message "$GREEN" "  ✓ 内存数据一致性验证通过 (used + free = total)"
                else
                    print_message "$YELLOW" "  ⚠ 内存数据一致性问题: $used1 + $free1 ≠ $total1"
                fi
            fi
        fi
    else
        print_message "$RED" "✗ 内存数据获取失败"
    fi
    
    echo
}

# 性能测试
test_performance() {
    print_message "$BLUE" "性能测试..."
    
    local iterations=5
    local total_time=0
    
    for i in $(seq 1 $iterations); do
        local start_time=$(date +%s.%N)
        
        # 执行完整的数据收集
        get_cpu_usage >/dev/null
        get_memory_usage >/dev/null
        get_disk_usage >/dev/null
        get_network_usage >/dev/null
        get_uptime >/dev/null
        
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "1")
        total_time=$(echo "$total_time + $duration" | bc 2>/dev/null || echo "$total_time")
        
        echo "  第 $i 次: ${duration}s"
    done
    
    local avg_time=$(echo "scale=3; $total_time / $iterations" | bc 2>/dev/null || echo "1")
    print_message "$GREEN" "✓ 平均数据收集时间: ${avg_time}s"
    
    if (( $(echo "$avg_time < 2.0" | bc -l 2>/dev/null || echo "1") )); then
        print_message "$GREEN" "  ✓ 性能良好"
    else
        print_message "$YELLOW" "  ⚠ 性能可能需要优化"
    fi
    
    echo
}

# 主测试函数
main() {
    print_message "$BLUE" "=================================="
    print_message "$BLUE" "    数据收集功能测试"
    print_message "$BLUE" "=================================="
    echo
    
    # 加载监控函数
    print_message "$BLUE" "加载监控函数..."
    load_monitoring_functions
    print_message "$GREEN" "✓ 监控函数加载完成"
    echo
    
    # 系统信息
    print_message "$BLUE" "系统信息:"
    echo "  操作系统: $(uname -s)"
    echo "  内核版本: $(uname -r)"
    echo "  架构: $(uname -m)"
    echo
    
    local failed_tests=0
    
    # 测试各个数据收集函数
    test_function "get_cpu_usage" "CPU使用率获取" || ((failed_tests++))
    test_function "get_memory_usage" "内存使用情况获取" || ((failed_tests++))
    test_function "get_disk_usage" "磁盘使用情况获取" || ((failed_tests++))
    test_function "get_network_usage" "网络使用情况获取" || ((failed_tests++))
    test_function "get_uptime" "系统运行时间获取" || ((failed_tests++))
    
    # 测试数据验证
    test_validation
    
    # 测试数据一致性
    test_data_consistency
    
    # 性能测试
    test_performance
    
    # 总结
    print_message "$BLUE" "=================================="
    if [[ $failed_tests -eq 0 ]]; then
        print_message "$GREEN" "✅ 所有测试通过！"
    else
        print_message "$YELLOW" "⚠ $failed_tests 个测试失败"
    fi
    print_message "$BLUE" "=================================="
}

# 显示帮助信息
show_help() {
    echo "数据收集功能测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo
    echo "此脚本将测试VPS监控脚本中的数据收集功能，包括："
    echo "  - CPU使用率获取"
    echo "  - 内存使用情况获取"
    echo "  - 磁盘使用情况获取"
    echo "  - 网络使用情况获取"
    echo "  - 系统运行时间获取"
    echo "  - 数据验证功能"
    echo "  - 数据一致性检查"
    echo "  - 性能测试"
}

# 检查参数
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# 运行主函数
main
