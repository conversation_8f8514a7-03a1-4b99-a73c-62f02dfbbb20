#!/bin/bash

# 错误处理和重试机制测试脚本
# 用于验证优化后的错误处理功能

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 从主脚本加载重试函数
load_retry_functions() {
    if [[ ! -f "cf-vps-monitor.sh" ]]; then
        print_message "$RED" "错误: 找不到 cf-vps-monitor.sh 文件"
        exit 1
    fi
    
    # 提取重试相关函数
    source <(awk '
    /^# 指数退避重试机制/,/^}/ { print; next }
    /^# 本地缓存管理/,/^}/ { print; next }
    /^# 初始化缓存目录/,/^}/ { print; next }
    /^# 保存配置到缓存/,/^}/ { print; next }
    /^# 从缓存加载配置/,/^}/ { print; next }
    /^# 清理过期缓存/,/^}/ { print; next }
    /^# 保存最后成功的监控数据/,/^}/ { print; next }
    /^# 获取缓存的监控数据/,/^}/ { print; next }
    ' cf-vps-monitor.sh)
    
    # 设置必要的变量
    SCRIPT_DIR="${HOME}/.cf-vps-monitor"
    export SCRIPT_DIR
}

# 模拟成功的函数
mock_success_function() {
    echo "模拟成功执行"
    return 0
}

# 模拟失败的函数
mock_failure_function() {
    local attempt_count="${1:-1}"
    echo "模拟失败执行 (尝试 $attempt_count)"
    return 1
}

# 模拟部分失败的函数（前几次失败，最后成功）
mock_partial_failure_function() {
    local max_failures="${1:-2}"
    local attempt_file="/tmp/test_attempt_count"
    
    local current_attempt=1
    if [[ -f "$attempt_file" ]]; then
        current_attempt=$(cat "$attempt_file")
    fi
    
    echo "模拟部分失败执行 (尝试 $current_attempt)"
    
    if [[ $current_attempt -le $max_failures ]]; then
        echo $((current_attempt + 1)) > "$attempt_file"
        return 1
    else
        rm -f "$attempt_file"
        return 0
    fi
}

# 测试重试机制
test_retry_mechanism() {
    print_message "$BLUE" "测试重试机制..."
    
    # 测试1: 成功的函数不应该重试
    print_message "$BLUE" "测试1: 成功函数（不应重试）"
    if retry_with_backoff 3 1 5 2 mock_success_function; then
        print_message "$GREEN" "✓ 成功函数测试通过"
    else
        print_message "$RED" "✗ 成功函数测试失败"
    fi
    echo
    
    # 测试2: 失败的函数应该重试指定次数
    print_message "$BLUE" "测试2: 失败函数（应重试3次）"
    local start_time=$(date +%s)
    if retry_with_backoff 3 1 5 2 mock_failure_function; then
        print_message "$RED" "✗ 失败函数不应该成功"
    else
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        print_message "$GREEN" "✓ 失败函数正确重试并最终失败 (耗时: ${duration}s)"
    fi
    echo
    
    # 测试3: 部分失败的函数应该在重试后成功
    print_message "$BLUE" "测试3: 部分失败函数（前2次失败，第3次成功）"
    rm -f /tmp/test_attempt_count  # 重置计数器
    if retry_with_backoff 3 1 5 2 mock_partial_failure_function 2; then
        print_message "$GREEN" "✓ 部分失败函数在重试后成功"
    else
        print_message "$RED" "✗ 部分失败函数测试失败"
    fi
    echo
}

# 测试缓存机制
test_cache_mechanism() {
    print_message "$BLUE" "测试缓存机制..."
    
    # 初始化缓存
    init_cache
    if [[ -d "$SCRIPT_DIR/cache" ]]; then
        print_message "$GREEN" "✓ 缓存目录创建成功"
    else
        print_message "$RED" "✗ 缓存目录创建失败"
        return 1
    fi
    
    # 测试配置缓存
    local test_config='{"report_interval":60,"enabled_metrics":["cpu","memory"]}'
    save_config_cache "$test_config"
    
    local loaded_config=$(load_config_cache)
    if [[ "$loaded_config" == "$test_config" ]]; then
        print_message "$GREEN" "✓ 配置缓存保存和加载成功"
    else
        print_message "$RED" "✗ 配置缓存测试失败"
        echo "  期望: $test_config"
        echo "  实际: $loaded_config"
    fi
    
    # 测试监控数据缓存
    local test_metrics='{"timestamp":1640995200,"cpu":{"usage_percent":25.5}}'
    save_metrics_cache "$test_metrics"
    
    # 由于缓存有时间限制，我们需要检查缓存文件是否存在
    if [[ -f "$SCRIPT_DIR/cache/last_metrics.json" ]]; then
        print_message "$GREEN" "✓ 监控数据缓存保存成功"
    else
        print_message "$RED" "✗ 监控数据缓存保存失败"
    fi
    
    echo
}

# 测试错误分类
test_error_classification() {
    print_message "$BLUE" "测试错误分类..."
    
    # 模拟不同的HTTP错误码
    local test_cases=(
        "400:不可重试"
        "401:不可重试"
        "404:不可重试"
        "429:可重试"
        "500:可重试"
        "503:可重试"
        "000:可重试"
    )
    
    for test_case in "${test_cases[@]}"; do
        local http_code="${test_case%:*}"
        local expected="${test_case#*:}"
        
        # 这里我们只是演示错误分类逻辑
        local result
        case "$http_code" in
            "400"|"401"|"404") result="不可重试" ;;
            "429"|"500"|"503"|"000") result="可重试" ;;
            *) result="未知" ;;
        esac
        
        if [[ "$result" == "$expected" ]]; then
            print_message "$GREEN" "✓ HTTP $http_code 错误分类正确: $result"
        else
            print_message "$RED" "✗ HTTP $http_code 错误分类错误: 期望 $expected, 实际 $result"
        fi
    done
    
    echo
}

# 性能测试
test_performance() {
    print_message "$BLUE" "性能测试..."
    
    # 测试重试机制的性能开销
    local iterations=10
    local total_time=0
    
    for i in $(seq 1 $iterations); do
        local start_time=$(date +%s.%N)
        
        # 执行一次成功的重试（不应该有重试开销）
        retry_with_backoff 3 1 5 2 mock_success_function >/dev/null
        
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "0.1")
        total_time=$(echo "$total_time + $duration" | bc 2>/dev/null || echo "$total_time")
    done
    
    local avg_time=$(echo "scale=3; $total_time / $iterations" | bc 2>/dev/null || echo "0.1")
    print_message "$GREEN" "✓ 重试机制平均开销: ${avg_time}s"
    
    if (( $(echo "$avg_time < 0.1" | bc -l 2>/dev/null || echo "1") )); then
        print_message "$GREEN" "  ✓ 性能开销可接受"
    else
        print_message "$YELLOW" "  ⚠ 性能开销较高"
    fi
    
    echo
}

# 清理测试环境
cleanup_test_environment() {
    print_message "$BLUE" "清理测试环境..."
    
    # 清理测试文件
    rm -f /tmp/test_attempt_count
    
    # 清理缓存目录（如果是测试创建的）
    if [[ -d "$SCRIPT_DIR/cache" ]]; then
        rm -rf "$SCRIPT_DIR/cache"
        print_message "$GREEN" "✓ 测试缓存目录已清理"
    fi
}

# 主测试函数
main() {
    print_message "$BLUE" "=================================="
    print_message "$BLUE" "    错误处理和重试机制测试"
    print_message "$BLUE" "=================================="
    echo
    
    # 加载重试函数
    print_message "$BLUE" "加载重试和缓存函数..."
    load_retry_functions
    print_message "$GREEN" "✓ 函数加载完成"
    echo
    
    local failed_tests=0
    
    # 运行各项测试
    test_retry_mechanism || ((failed_tests++))
    test_cache_mechanism || ((failed_tests++))
    test_error_classification || ((failed_tests++))
    test_performance || ((failed_tests++))
    
    # 清理测试环境
    cleanup_test_environment
    
    # 总结
    print_message "$BLUE" "=================================="
    if [[ $failed_tests -eq 0 ]]; then
        print_message "$GREEN" "✅ 所有错误处理测试通过！"
    else
        print_message "$YELLOW" "⚠ $failed_tests 个测试失败"
    fi
    print_message "$BLUE" "=================================="
}

# 显示帮助信息
show_help() {
    echo "错误处理和重试机制测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo
    echo "此脚本将测试VPS监控脚本中的错误处理功能，包括："
    echo "  - 指数退避重试机制"
    echo "  - 本地缓存机制"
    echo "  - 错误分类处理"
    echo "  - 性能测试"
}

# 检查参数
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# 运行主函数
main
