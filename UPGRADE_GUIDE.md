# VPS监控系统升级指南

## 🎯 新功能概述

本次升级主要解决了**VPS脚本无法主动获取面板配置**的问题，新增了独立的配置获取API，让VPS脚本能够主动同步服务器配置。

## ✨ 主要改进

### 1. 新增配置获取API
- **端点**: `GET /api/config/{server_id}`
- **认证**: 使用 `X-API-Key` 头部
- **功能**: VPS脚本可主动获取最新配置，包括上报间隔等设置

### 2. 智能配置同步
- VPS脚本启动时自动获取配置
- 定期检查配置更新（每10个周期约10分钟）
- 配置变更时自动应用新设置

### 3. 增强的错误处理
- 配置获取失败时使用本地缓存
- 详细的错误分类和处理
- 改进的日志记录

## 🚀 升级步骤

### 步骤1: 更新Worker代码
1. 将新的 `worker.js` 部署到 Cloudflare Workers
2. 新代码向后兼容，不会影响现有VPS脚本

### 步骤2: 更新VPS脚本
1. 下载新的 `cf-vps-monitor.sh` 脚本
2. 停止现有监控服务：
   ```bash
   ./cf-vps-monitor.sh stop
   ```
3. 备份现有配置（可选）：
   ```bash
   cp ~/.cf-vps-monitor/config ~/.cf-vps-monitor/config.backup
   ```
4. 使用新脚本重新安装：
   ```bash
   ./cf-vps-monitor.sh install
   ```

### 步骤3: 验证功能
1. 检查服务状态：
   ```bash
   ./cf-vps-monitor.sh status
   ```
2. 查看日志确认配置获取：
   ```bash
   ./cf-vps-monitor.sh logs
   ```
3. 运行测试脚本（可选）：
   ```bash
   export WORKER_URL="https://your-worker.domain.workers.dev"
   export SERVER_ID="your-server-id"
   export API_KEY="your-api-key"
   ./test-config-api.sh
   ```

## 📋 API文档

### 配置获取API

**请求**:
```http
GET /api/config/{server_id}
Headers:
  X-API-Key: {api_key}
```

**响应**:
```json
{
  "success": true,
  "config": {
    "report_interval": 60,
    "enabled_metrics": ["cpu", "memory", "disk", "network", "uptime"],
    "server_info": {
      "id": "server123",
      "name": "Web Server 1",
      "description": "Production web server"
    }
  },
  "timestamp": 1640995200
}
```

**错误响应**:
```json
{
  "error": "Invalid API key",
  "message": "API密钥无效"
}
```

## 🔧 配置说明

### 自动配置同步
- VPS脚本启动时会自动获取最新配置
- 每10个上报周期（约10分钟）检查一次配置更新
- 配置变更时自动应用，无需重启服务

### 配置优先级
1. 服务器端配置（通过API获取）
2. 本地配置文件
3. 默认配置

## 🐛 故障排除

### 常见问题诊断

#### HTTP 404错误（服务器不存在）
**症状**: 配置获取失败，返回404状态码
**原因**: 服务器ID在监控面板中不存在
**解决方案**:
1. 登录监控面板，检查服务器列表
2. 确认服务器ID是否正确拼写
3. 如果服务器不存在，在面板中添加新服务器
4. 重新运行配置: `./cf-vps-monitor.sh config`

#### HTTP 401错误（认证失败）
**症状**: API调用返回401状态码
**原因**: API密钥无效或过期
**解决方案**:
1. 检查API密钥是否正确
2. 确认API密钥没有过期
3. 重新获取API密钥并更新配置

#### 网络连接问题
**症状**: 连接超时或网络错误
**解决方案**:
1. 检查防火墙设置
2. 确认可以访问外网
3. 检查代理设置
4. 尝试使用不同的DNS服务器

### 诊断工具
使用内置的诊断脚本快速定位问题：
```bash
./diagnose-connection.sh
```

该脚本会自动检查：
- 配置文件完整性
- 网络连接状态
- API端点可用性
- 提供修复建议

### 兼容性问题
- 新版本完全向后兼容
- 旧版本VPS脚本仍可正常工作
- 建议逐步升级以享受新功能

## 📊 监控和日志

### 关键日志信息
- `正在获取服务器配置...` - 配置获取开始
- `配置获取成功` - 配置获取成功
- `检测到新的上报间隔: X秒` - 配置更新
- `配置无变化` - 配置检查完成，无更新

### 性能影响
- 配置获取请求轻量级，对性能影响极小
- 失败时自动降级，不影响数据上报
- 智能缓存机制减少不必要的请求

## 🔄 回滚方案

如果需要回滚到旧版本：
1. 停止新版本服务
2. 恢复旧版本脚本
3. 恢复配置文件备份
4. 重新启动服务

旧版本Worker代码仍然支持，可以安全回滚。

## 📞 技术支持

如果在升级过程中遇到问题：
1. 查看详细日志：`./cf-vps-monitor.sh logs`
2. 运行测试脚本验证连接
3. 检查Worker日志（Cloudflare Dashboard）
4. 确认配置参数正确性

## 🎉 升级完成

升级完成后，您的VPS监控系统将具备：
- ✅ 主动配置获取能力
- ✅ 智能配置同步
- ✅ 增强的错误处理
- ✅ 更好的可靠性

享受更智能的VPS监控体验！
