#!/bin/bash

# 系统兼容性测试脚本
# 用于验证VPS监控脚本在不同系统上的兼容性

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 从主脚本加载系统检测函数
load_system_functions() {
    if [[ ! -f "cf-vps-monitor.sh" ]]; then
        print_message "$RED" "错误: 找不到 cf-vps-monitor.sh 文件"
        exit 1
    fi
    
    # 提取系统检测相关函数
    source <(awk '
    /^# 检查命令是否存在/,/^}/ { print; next }
    /^# 统一的命令接口/,/^}/ { print; next }
    /^# 跨平台的命令执行/,/^}/ { print; next }
    /^# 检测系统信息/,/^}/ { print; next }
    /^# 检测包管理器/,/^}/ { print; next }
    ' cf-vps-monitor.sh)
}

# 测试系统检测功能
test_system_detection() {
    print_message "$BLUE" "测试系统检测功能..."
    
    # 执行系统检测
    detect_system
    
    # 验证基本变量
    local required_vars=("OS" "ARCH" "KERNEL_VERSION" "DISTRO_ID" "DISTRO_NAME")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -eq 0 ]]; then
        print_message "$GREEN" "✓ 系统检测成功"
        echo "  操作系统: $OS"
        echo "  架构: $ARCH"
        echo "  内核版本: $KERNEL_VERSION"
        echo "  发行版ID: $DISTRO_ID"
        echo "  发行版名称: $DISTRO_NAME"
        echo "  容器环境: ${IS_CONTAINER:-false}"
        echo "  虚拟化: ${VIRTUALIZATION:-none}"
    else
        print_message "$RED" "✗ 系统检测失败，缺少变量: ${missing_vars[*]}"
        return 1
    fi
    
    echo
}

# 测试包管理器检测
test_package_manager_detection() {
    print_message "$BLUE" "测试包管理器检测..."
    
    detect_package_manager
    
    if [[ -n "${PKG_MANAGER:-}" ]]; then
        print_message "$GREEN" "✓ 包管理器检测成功: $PKG_MANAGER"
        echo "  安装命令: $PKG_INSTALL"
        echo "  更新命令: $PKG_UPDATE"
        echo "  搜索命令: ${PKG_SEARCH:-N/A}"
        echo "  信息命令: ${PKG_INFO:-N/A}"
    else
        print_message "$YELLOW" "⚠ 未检测到包管理器"
    fi
    
    echo
}

# 测试命令接口
test_command_interface() {
    print_message "$BLUE" "测试统一命令接口..."
    
    local command_types=("memory_info" "disk_usage" "network_stats" "cpu_info" "process_info")
    local success_count=0
    
    for cmd_type in "${command_types[@]}"; do
        local method=$(get_system_command "$cmd_type" "none")
        if [[ "$method" != "none" ]]; then
            print_message "$GREEN" "✓ $cmd_type: $method"
            ((success_count++))
        else
            print_message "$YELLOW" "⚠ $cmd_type: 无可用方法"
        fi
    done
    
    if [[ $success_count -eq ${#command_types[@]} ]]; then
        print_message "$GREEN" "✓ 所有命令接口可用"
    elif [[ $success_count -gt 0 ]]; then
        print_message "$YELLOW" "⚠ 部分命令接口可用 ($success_count/${#command_types[@]})"
    else
        print_message "$RED" "✗ 无可用命令接口"
        return 1
    fi
    
    echo
}

# 测试跨平台命令执行
test_cross_platform_commands() {
    print_message "$BLUE" "测试跨平台命令执行..."
    
    local test_cases=(
        "memory_info:$(get_system_command memory_info)"
        "cpu_info:$(get_system_command cpu_info)"
        "disk_usage:$(get_system_command disk_usage)"
        "network_stats:$(get_system_command network_stats)"
    )
    
    local success_count=0
    
    for test_case in "${test_cases[@]}"; do
        local cmd_type="${test_case%:*}"
        local method="${test_case#*:}"
        
        if [[ "$method" != "none" ]]; then
            if execute_system_command "$cmd_type" "$method" >/dev/null 2>&1; then
                print_message "$GREEN" "✓ $cmd_type ($method) 执行成功"
                ((success_count++))
            else
                print_message "$YELLOW" "⚠ $cmd_type ($method) 执行失败"
            fi
        else
            print_message "$YELLOW" "⚠ $cmd_type: 无可用方法"
        fi
    done
    
    if [[ $success_count -gt 0 ]]; then
        print_message "$GREEN" "✓ 跨平台命令测试完成 ($success_count 个成功)"
    else
        print_message "$RED" "✗ 所有跨平台命令测试失败"
        return 1
    fi
    
    echo
}

# 测试依赖命令可用性
test_dependency_availability() {
    print_message "$BLUE" "测试依赖命令可用性..."
    
    local required_commands=("curl" "date" "uname" "cat" "grep" "awk" "sed")
    local optional_commands=("bc" "jq" "ifstat" "top" "vmstat" "free" "df" "ps")
    
    local required_missing=()
    local optional_missing=()
    
    # 检查必需命令
    for cmd in "${required_commands[@]}"; do
        if command_exists "$cmd"; then
            print_message "$GREEN" "✓ $cmd (必需)"
        else
            print_message "$RED" "✗ $cmd (必需)"
            required_missing+=("$cmd")
        fi
    done
    
    # 检查可选命令
    for cmd in "${optional_commands[@]}"; do
        if command_exists "$cmd"; then
            print_message "$GREEN" "✓ $cmd (可选)"
        else
            print_message "$YELLOW" "⚠ $cmd (可选)"
            optional_missing+=("$cmd")
        fi
    done
    
    echo
    print_message "$CYAN" "依赖检查总结:"
    if [[ ${#required_missing[@]} -eq 0 ]]; then
        print_message "$GREEN" "✓ 所有必需依赖可用"
    else
        print_message "$RED" "✗ 缺少必需依赖: ${required_missing[*]}"
    fi
    
    if [[ ${#optional_missing[@]} -eq 0 ]]; then
        print_message "$GREEN" "✓ 所有可选依赖可用"
    else
        print_message "$YELLOW" "⚠ 缺少可选依赖: ${optional_missing[*]}"
    fi
    
    echo
    
    # 返回状态
    if [[ ${#required_missing[@]} -gt 0 ]]; then
        return 1
    fi
}

# 测试文件系统权限
test_filesystem_permissions() {
    print_message "$BLUE" "测试文件系统权限..."
    
    local test_dirs=(
        "$HOME/.cf-vps-monitor"
        "/tmp/vps-monitor-test"
        "$HOME/.local/share/vps-monitor"
    )
    
    local writable_dirs=()
    
    for dir in "${test_dirs[@]}"; do
        if mkdir -p "$dir" 2>/dev/null && touch "$dir/test_file" 2>/dev/null; then
            print_message "$GREEN" "✓ $dir (可写)"
            writable_dirs+=("$dir")
            rm -f "$dir/test_file"
            rmdir "$dir" 2>/dev/null || true
        else
            print_message "$YELLOW" "⚠ $dir (不可写)"
        fi
    done
    
    if [[ ${#writable_dirs[@]} -gt 0 ]]; then
        print_message "$GREEN" "✓ 找到可写目录: ${writable_dirs[0]}"
    else
        print_message "$RED" "✗ 没有找到可写目录"
        return 1
    fi
    
    echo
}

# 生成兼容性报告
generate_compatibility_report() {
    print_message "$BLUE" "生成兼容性报告..."
    
    local report_file="system_compatibility_report.txt"
    
    cat > "$report_file" << EOF
VPS监控系统兼容性报告
生成时间: $(date)

=== 系统信息 ===
操作系统: ${OS:-未知}
架构: ${ARCH:-未知}
内核版本: ${KERNEL_VERSION:-未知}
发行版: ${DISTRO_NAME:-未知} (${DISTRO_ID:-未知})
容器环境: ${IS_CONTAINER:-false}
虚拟化: ${VIRTUALIZATION:-none}

=== 包管理器 ===
包管理器: ${PKG_MANAGER:-无}
安装命令: ${PKG_INSTALL:-N/A}
更新命令: ${PKG_UPDATE:-N/A}

=== 命令可用性 ===
EOF
    
    # 添加命令可用性信息
    local commands=("curl" "bc" "jq" "top" "vmstat" "free" "df" "ps" "ifstat")
    for cmd in "${commands[@]}"; do
        if command_exists "$cmd"; then
            echo "$cmd: 可用" >> "$report_file"
        else
            echo "$cmd: 不可用" >> "$report_file"
        fi
    done
    
    print_message "$GREEN" "✓ 兼容性报告已生成: $report_file"
    echo
}

# 主测试函数
main() {
    print_message "$BLUE" "=================================="
    print_message "$BLUE" "    系统兼容性测试"
    print_message "$BLUE" "=================================="
    echo
    
    # 加载系统函数
    print_message "$BLUE" "加载系统检测函数..."
    load_system_functions
    print_message "$GREEN" "✓ 函数加载完成"
    echo
    
    local failed_tests=0
    
    # 运行各项测试
    test_system_detection || ((failed_tests++))
    test_package_manager_detection || ((failed_tests++))
    test_command_interface || ((failed_tests++))
    test_cross_platform_commands || ((failed_tests++))
    test_dependency_availability || ((failed_tests++))
    test_filesystem_permissions || ((failed_tests++))
    
    # 生成报告
    generate_compatibility_report
    
    # 总结
    print_message "$BLUE" "=================================="
    if [[ $failed_tests -eq 0 ]]; then
        print_message "$GREEN" "✅ 系统兼容性测试全部通过！"
        print_message "$GREEN" "此系统完全支持VPS监控脚本"
    elif [[ $failed_tests -le 2 ]]; then
        print_message "$YELLOW" "⚠ 系统兼容性测试部分通过 ($failed_tests 个问题)"
        print_message "$YELLOW" "此系统基本支持VPS监控脚本，但可能有功能限制"
    else
        print_message "$RED" "❌ 系统兼容性测试失败 ($failed_tests 个问题)"
        print_message "$RED" "此系统可能不完全支持VPS监控脚本"
    fi
    print_message "$BLUE" "=================================="
}

# 显示帮助信息
show_help() {
    echo "系统兼容性测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo
    echo "此脚本将测试当前系统对VPS监控脚本的兼容性，包括："
    echo "  - 系统信息检测"
    echo "  - 包管理器检测"
    echo "  - 命令接口测试"
    echo "  - 跨平台命令执行"
    echo "  - 依赖命令可用性"
    echo "  - 文件系统权限"
}

# 检查参数
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# 运行主函数
main
