#!/bin/bash

# 配置获取API测试脚本
# 用于验证新增的配置获取功能

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 测试配置
WORKER_URL="${WORKER_URL:-}"
SERVER_ID="${SERVER_ID:-}"
API_KEY="${API_KEY:-}"

# 检查环境变量
check_environment() {
    print_message "$BLUE" "检查测试环境..."
    
    if [[ -z "$WORKER_URL" ]]; then
        print_message "$RED" "错误: 请设置 WORKER_URL 环境变量"
        echo "示例: export WORKER_URL='https://your-worker.your-domain.workers.dev'"
        exit 1
    fi
    
    if [[ -z "$SERVER_ID" ]]; then
        print_message "$RED" "错误: 请设置 SERVER_ID 环境变量"
        echo "示例: export SERVER_ID='your-server-id'"
        exit 1
    fi
    
    if [[ -z "$API_KEY" ]]; then
        print_message "$RED" "错误: 请设置 API_KEY 环境变量"
        echo "示例: export API_KEY='your-api-key'"
        exit 1
    fi
    
    print_message "$GREEN" "✓ 环境变量检查通过"
    echo "  Worker URL: $WORKER_URL"
    echo "  Server ID: $SERVER_ID"
    echo "  API Key: ${API_KEY:0:8}..."
}

# 测试配置获取API
test_config_api() {
    print_message "$BLUE" "测试配置获取API..."
    
    local response=$(curl -s -w "%{http_code}" -X GET "$WORKER_URL/api/config/$SERVER_ID" \
        -H "X-API-Key: $API_KEY" 2>/dev/null || echo "000")
    
    local http_code="${response: -3}"
    local response_body="${response%???}"
    
    echo "HTTP状态码: $http_code"
    echo "响应内容: $response_body"
    
    if [[ "$http_code" == "200" ]]; then
        print_message "$GREEN" "✓ 配置获取API测试成功"
        
        # 尝试解析配置
        if command -v jq >/dev/null 2>&1; then
            print_message "$BLUE" "使用jq解析响应:"
            echo "$response_body" | jq '.' 2>/dev/null || echo "JSON解析失败"
            
            local interval=$(echo "$response_body" | jq -r '.config.report_interval // empty' 2>/dev/null)
            if [[ -n "$interval" ]]; then
                print_message "$GREEN" "✓ 成功解析上报间隔: ${interval}秒"
            else
                print_message "$YELLOW" "⚠ 无法解析上报间隔"
            fi
        else
            print_message "$BLUE" "使用sed解析响应:"
            local interval=$(echo "$response_body" | sed -n 's/.*"report_interval":\([0-9]\+\).*/\1/p')
            if [[ -n "$interval" ]]; then
                print_message "$GREEN" "✓ 成功解析上报间隔: ${interval}秒"
            else
                print_message "$YELLOW" "⚠ 无法解析上报间隔"
            fi
        fi
        
        return 0
    else
        print_message "$RED" "✗ 配置获取API测试失败"
        
        case "$http_code" in
            "400") print_message "$RED" "错误: 无效的服务器ID格式" ;;
            "401") print_message "$RED" "错误: API密钥无效或缺失" ;;
            "404") print_message "$RED" "错误: 服务器不存在" ;;
            "500") print_message "$RED" "错误: 服务器内部错误" ;;
            "000") print_message "$RED" "错误: 网络连接失败" ;;
            *) print_message "$RED" "错误: 未知HTTP状态码 $http_code" ;;
        esac
        
        return 1
    fi
}

# 测试数据上报API（用于对比）
test_report_api() {
    print_message "$BLUE" "测试数据上报API（用于对比）..."
    
    local timestamp=$(date +%s)
    local test_data="{\"timestamp\":$timestamp,\"cpu\":{\"usage_percent\":10.5,\"load_avg\":[0.1,0.2,0.3]},\"memory\":{\"total\":1000,\"used\":500,\"free\":500,\"usage_percent\":50.0},\"disk\":{\"total\":10.0,\"used\":5.0,\"free\":5.0,\"usage_percent\":50},\"network\":{\"upload_speed\":1000,\"download_speed\":2000,\"total_upload\":1000000,\"total_download\":2000000},\"uptime\":3600}"
    
    local response=$(curl -s -w "%{http_code}" -X POST "$WORKER_URL/api/report/$SERVER_ID" \
        -H "Content-Type: application/json" \
        -H "X-API-Key: $API_KEY" \
        -d "$test_data" 2>/dev/null || echo "000")
    
    local http_code="${response: -3}"
    local response_body="${response%???}"
    
    echo "HTTP状态码: $http_code"
    echo "响应内容: $response_body"
    
    if [[ "$http_code" == "200" ]]; then
        print_message "$GREEN" "✓ 数据上报API测试成功"
        
        # 检查响应中的间隔设置
        if command -v jq >/dev/null 2>&1; then
            local interval=$(echo "$response_body" | jq -r '.interval // empty' 2>/dev/null)
            if [[ -n "$interval" ]]; then
                print_message "$GREEN" "✓ 上报响应中包含间隔设置: ${interval}秒"
            fi
        else
            local interval=$(echo "$response_body" | sed -n 's/.*"interval":\([0-9]\+\).*/\1/p')
            if [[ -n "$interval" ]]; then
                print_message "$GREEN" "✓ 上报响应中包含间隔设置: ${interval}秒"
            fi
        fi
        
        return 0
    else
        print_message "$RED" "✗ 数据上报API测试失败 (HTTP $http_code)"
        return 1
    fi
}

# 主函数
main() {
    print_message "$BLUE" "=================================="
    print_message "$BLUE" "    配置获取API功能测试"
    print_message "$BLUE" "=================================="
    echo
    
    check_environment
    echo
    
    test_config_api
    echo
    
    test_report_api
    echo
    
    print_message "$GREEN" "测试完成！"
}

# 显示帮助信息
show_help() {
    echo "配置获取API测试脚本"
    echo
    echo "用法: $0"
    echo
    echo "环境变量:"
    echo "  WORKER_URL  - Cloudflare Worker URL"
    echo "  SERVER_ID   - 服务器ID"
    echo "  API_KEY     - API密钥"
    echo
    echo "示例:"
    echo "  export WORKER_URL='https://your-worker.your-domain.workers.dev'"
    echo "  export SERVER_ID='your-server-id'"
    echo "  export API_KEY='your-api-key'"
    echo "  $0"
}

# 检查参数
if [[ "${1:-}" == "-h" || "${1:-}" == "--help" ]]; then
    show_help
    exit 0
fi

# 运行主函数
main
